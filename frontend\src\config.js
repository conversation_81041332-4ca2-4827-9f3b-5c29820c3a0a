/**
 * Global configuration settings for the ACE Social frontend
 */

import { getPlatformConfig, logEnvironmentInfo } from './utils/environmentDetection.js';

// Get platform-specific configuration (with enhanced safety checks)
let platformConfig;

// Safe initialization function
const initializePlatformConfig = () => {
  try {
    if (typeof window !== 'undefined' && window.location) {
      platformConfig = getPlatformConfig();
      // Log environment information in development only
      if (process.env.NODE_ENV === 'development') {
        logEnvironmentInfo();
      }
    } else {
      // Fallback configuration for SSR or when window is not available
      platformConfig = {
        environment: 'development',
        supportsWebSocket: false,
        useFallbackMode: true,
        pollInterval: 5000,
        enableRealTimeUpdates: false,
        enableMetrics: false,
        enableDebugLogging: false
      };
    }
  } catch (error) {
    console.warn('[Config] Error getting platform config, using fallback:', error);
    platformConfig = {
      environment: 'development',
      supportsWebSocket: false,
      useFallbackMode: true,
      pollInterval: 5000,
      enableRealTimeUpdates: false,
      enableMetrics: false,
      enableDebugLogging: false
    };
  }
};

// Initialize with delay to ensure DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePlatformConfig);
  } else {
    // DOM is already ready
    setTimeout(initializePlatformConfig, 0);
  }
} else {
  // Server-side or non-browser environment
  initializePlatformConfig();
}

// API base URL from environment variables or fallback to localhost
export const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:8001";

// Feature flags - Mock data is always disabled in production
export const ENABLE_MOCK_DATA = false; // Hardcoded to false to ensure no mock data is used

// Safe getter for platform config
export const getPlatformConfigSafe = () => {
  if (!platformConfig) {
    // Return safe defaults if not initialized yet
    return {
      environment: 'development',
      supportsWebSocket: false,
      useFallbackMode: true,
      pollInterval: 5000,
      enableRealTimeUpdates: false,
      enableMetrics: false,
      enableDebugLogging: false
    };
  }
  return platformConfig;
};

// Environment
export const ENVIRONMENT = import.meta.env.VITE_ENVIRONMENT || 'development';

// WebSocket configuration (using safe getter)
export const getWebSocketEnabled = () => getPlatformConfigSafe().supportsWebSocket;
export const getUseFallbackMode = () => getPlatformConfigSafe().useFallbackMode;
export const getPollInterval = () => getPlatformConfigSafe().pollInterval;

// Legacy exports for backward compatibility
export const WEBSOCKET_ENABLED = false; // Default to false for safety
export const USE_FALLBACK_MODE = true; // Default to true for safety
export const POLL_INTERVAL = 5000; // Default poll interval

// Lemon Squeezy store ID
export const LEMON_SQUEEZY_STORE_ID = import.meta.env
  .VITE_LEMON_SQUEEZY_STORE_ID;

// App version
export const APP_VERSION = "0.1.0";

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;

// Date format settings
export const DEFAULT_DATE_FORMAT = "MMM d, yyyy";
export const DEFAULT_TIME_FORMAT = "h:mm a";
export const DEFAULT_DATETIME_FORMAT = "MMM d, yyyy h:mm a";

// Image settings
export const MAX_IMAGE_UPLOAD_SIZE = 5 * 1024 * 1024; // 5MB
export const SUPPORTED_IMAGE_FORMATS = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];

// Referral program settings
export const REFERRAL_REWARD_THRESHOLD = 5; // Number of successful referrals needed for a reward
export const REFERRAL_REWARD_AMOUNT = 50; // Number of credits awarded per reward

// Social media platforms
export const SUPPORTED_PLATFORMS = [
  "linkedin",
  "twitter",
  "facebook",
  "instagram",
  "youtube",
  "pinterest",
  "tiktok",
  "reddit",
  "threads",
];

// Analytics refresh intervals (in milliseconds)
export const ANALYTICS_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes

// WebSocket configuration
export const WS_RECONNECT_INTERVAL = 3000; // 3 seconds
export const WS_MAX_RECONNECT_ATTEMPTS = 5;

// Toast notification defaults
export const DEFAULT_TOAST_DURATION = 5000; // 5 seconds
export const DEFAULT_TOAST_POSITION = "bottom-right";

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: "token",
  REFRESH_TOKEN: "refreshToken",
  USER_PREFERENCES: "userPreferences",
  DASHBOARD_LAYOUT: "dashboardLayout",
  THEME_MODE: "themeMode",
  LAST_VISITED_PAGE: "lastVisitedPage",
  SIDEBAR_STATE: "sidebarState",
  SIDEBAR_PREFERENCES: "sidebarPreferences",
};

// API configuration
export const DEFAULT_API_TIMEOUT = parseInt(
  import.meta.env.VITE_API_TIMEOUT || "15000",
  10
);
export const API_RETRY_ATTEMPTS = parseInt(
  import.meta.env.VITE_API_RETRY_ATTEMPTS || "3",
  10
);
export const FRONTEND_URL =
  import.meta.env.VITE_FRONTEND_URL || "http://localhost:3001";
