{"name": "ace-social-tool", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" vite --host", "dev:kill": "node kill-port-and-start.js", "dev:memory": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" vite --host", "dev:fast": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" vite --force --host", "dev:minimal": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" vite --host", "dev:windows": "set NODE_OPTIONS=--max-old-space-size=4096 && vite --host", "dev:windows:memory": "set NODE_OPTIONS=--max-old-space-size=8192 && vite --host", "dev:simple": "vite --host", "dev:stable": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" vite --config vite.config.stable.js --host", "dev:no-hmr": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" vite --config vite.config.stable.js --host --force", "dev:ultra-stable": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" vite --config vite.config.stable.js --host --no-hmr", "dev:clean": "rimraf node_modules/.vite && npm run dev:stable", "dev:debug": "cross-env DEBUG=vite:* vite --config vite.config.stable.js --host", "dev:mui-fix": "npm run clean:cache && npm run dev:stable", "dev:memory-optimized": "node clear-cache-and-start.js", "dev:minimal-memory": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" vite --config vite.config.memory.js --host", "clean:cache": "rimraf node_modules/.vite && rimraf .vite", "clear-cache": "node clear-cache-and-rebuild.js", "clear-vite-cache": "node clear-cache.js", "optimize": "npm run clear-cache && npm run dev:memory", "dev:force-rebuild": "npm run clean:cache && vite --force --host", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" vite build", "build:no-lint": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" SKIP_LINT=true vite build", "build:railway": "vite build", "build:analyze": "npm run build && npm run analyze", "build:production": "cross-env NODE_ENV=production NODE_OPTIONS=\"--max-old-space-size=4096\" vite build --mode production", "build:production:analyze": "npm run build:production && npm run analyze", "prebuild:production": "npm run lint && npm run type-check", "analyze": "npx serve dist -s", "clean": "rimraf node_modules/.vite && rimraf dist && npm cache clean --force", "clean:all": "rimraf node_modules && npm install", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest --coverage", "test:a11y": "vitest run --testPathPattern=accessibility", "test:integration": "vitest run --testPathPattern=integration", "test:mobile-responsive": "playwright test --config=playwright.mobile.config.js", "test:accessibility": "playwright test --config=playwright.a11y.config.js", "test:cross-browser": "playwright test --config=playwright.cross-browser.config.js", "test:e2e": "playwright test", "lighthouse": "lighthouse http://localhost:3000 --output=json --output=html --output-path=./lighthouse-report", "lighthouse:ci": "lhci autorun", "performance:audit": "npm run build && npm run lighthouse", "deploy:staging": "NODE_ENV=staging node scripts/deploy-production.js", "deploy:production": "NODE_ENV=production node scripts/deploy-production.js", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@faker-js/faker": "^8.4.1", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.176", "@mui/material": "^5.16.7", "@mui/styled-engine": "^7.2.0", "@mui/styled-engine-sc": "^7.2.0", "@mui/system": "^5.16.7", "@mui/x-charts": "^8.7.0", "@mui/x-data-grid": "^7.18.0", "@mui/x-data-grid-pro": "^8.7.0", "@mui/x-date-pickers": "^7.18.0", "@nivo/bar": "^0.93.0", "@nivo/core": "^0.99.0", "@nivo/geo": "^0.93.0", "@nivo/heatmap": "^0.98.0", "@nivo/line": "^0.93.0", "@nivo/pie": "^0.93.0", "@nivo/radar": "^0.99.0", "@nivo/scatterplot": "^0.99.0", "antd": "^5.26.3", "axios": "^1.4.0", "chart.js": "^4.4.9", "d3": "^7.8.5", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "file-saver": "^2.0.5", "formik": "^2.4.3", "framer-motion": "^12.23.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^3.1.2", "lodash-es": "^4.17.21", "notistack": "^3.0.2", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-error-boundary": "^6.0.0", "react-helmet-async": "^2.0.5", "react-image-crop": "^11.0.10", "react-is": "^18.3.1", "react-router-dom": "^6.22.3", "react-use-websocket": "^4.13.0", "recharts": "^2.15.4", "yup": "^1.2.0"}, "devDependencies": {"@lhci/cli": "^0.13.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.2.67", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.2.1", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^24.0.0", "lighthouse": "^12.6.0", "rimraf": "^5.0.5", "rollup-plugin-visualizer": "^6.0.0", "serve": "^14.2.4", "terser": "^5.39.2", "uuid": "^11.1.0", "vite": "^5.2.6", "vitest": "^1.3.1", "webpack-bundle-analyzer": "^4.10.2"}, "overrides": {"react-is": "^18.3.1", "@mui/material": "^5.16.7", "@mui/system": "^5.16.7", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0"}, "resolutions": {"react-is": "^18.3.1", "@mui/material": "^5.16.7", "@mui/system": "^5.16.7", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0"}}