{"name": "ace-social-platform", "version": "1.0.0", "description": "ACE Social Platform - Full Stack Application", "private": true, "scripts": {"install:all": "npm install && cd frontend && npm install && cd ../admin-app && npm install && cd ../backend && pip install -r requirements.txt", "dev": "concurrently --kill-others-on-fail \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:admin\"", "dev:frontend": "cd frontend && npm run dev", "dev:admin": "cd admin-app && npm run dev", "dev:backend": "cd backend && python start_server.py", "dev:windows": "concurrently --kill-others-on-fail \"npm run dev:backend:windows\" \"npm run dev:frontend:windows\"", "dev:backend:windows": "cd backend && python start_server.py", "dev:frontend:windows": "cd frontend && npm run dev:windows", "dev:memory": "concurrently --kill-others-on-fail \"npm run dev:backend\" \"npm run dev:frontend:memory\"", "dev:frontend:memory": "cd frontend && npm run dev:memory", "dev:simple": "concurrently --kill-others-on-fail \"npm run dev:backend\" \"npm run dev:frontend:simple\"", "dev:frontend:simple": "cd frontend && npm run dev:simple", "dev:stable": "concurrently --kill-others-on-fail \"npm run dev:backend\" \"npm run dev:frontend:stable\"", "dev:frontend:stable": "cd frontend && npm run dev:stable", "dev:ultra-stable": "concurrently --kill-others-on-fail \"npm run dev:backend\" \"npm run dev:frontend:ultra-stable\"", "dev:frontend:ultra-stable": "cd frontend && npm run dev:ultra-stable", "dev:no-reload": "concurrently --kill-others-on-fail \"npm run dev:backend\" \"npm run dev:frontend:no-reload\"", "dev:frontend:no-reload": "cd frontend && npm run dev:no-reload", "build": "npm run build:frontend:no-lint && npm run build:admin && npm run build:backend", "build:no-lint": "npm run build:frontend:no-lint && npm run build:admin && npm run build:backend", "build:frontend": "cd frontend && npm run build:production", "build:frontend:no-lint": "cd frontend && npm run build:no-lint", "build:admin": "cd admin-app && npm run build", "build:backend": "cd backend && python -m pip install -e .", "build:docker": "docker-compose build", "build:production": "npm run build && npm run build:docker", "start": "docker-compose up", "start:dev": "docker-compose -f docker-compose.dev.yml up", "start:prod": "docker-compose -f docker-compose.prod.yml up -d", "start:staging": "docker-compose -f docker-compose.staging.yml up -d", "start:backend": "cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000", "start:frontend": "cd frontend && npm run preview", "stop": "docker-compose down", "clean": "npm run clean:frontend && npm run clean:backend && docker system prune -f", "clean:frontend": "cd frontend && npm run clean", "clean:backend": "cd backend && find . -type d -name __pycache__ -exec rm -rf {} + 2>/dev/null || true", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && python -m pytest", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && python -m flake8 .", "deploy:staging": "npm run build:production && docker-compose -f docker-compose.staging.yml up -d", "deploy:production": "npm run build:production && docker-compose -f docker-compose.prod.yml up -d", "logs": "docker-compose logs -f", "logs:backend": "docker-compose logs -f backend", "logs:frontend": "docker-compose logs -f frontend", "logs:mongodb": "docker-compose logs -f mongodb", "logs:redis": "docker-compose logs -f redis", "health": "curl -f http://localhost:8000/health && curl -f http://localhost:3000", "verify": "node scripts/verify-build.js", "docker:build": "docker-compose build --no-cache", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:restart": "docker-compose restart", "docker:ps": "docker-compose ps", "docker:exec:backend": "docker-compose exec backend bash", "docker:exec:frontend": "docker-compose exec frontend sh", "docker:exec:mongodb": "docker-compose exec mongodb mongosh", "docker:exec:redis": "docker-compose exec redis redis-cli", "docker:backup:mongodb": "docker-compose exec mongodb mongodump --out /backups", "docker:restore:mongodb": "docker-compose exec mongodb mongorestore /backups", "docker:prune": "docker system prune -af && docker volume prune -f", "verify:build": "npm run verify", "build:analyze": "npm run build:frontend:analyze && npm run build:admin:analyze", "build:frontend:analyze": "cd frontend && npm run build:analyze", "build:admin:analyze": "cd admin-app && npm run build:analyze", "start:test": "docker-compose -f docker-compose.test.yml up -d", "stop:all": "docker-compose -f docker-compose.dev.yml down && docker-compose -f docker-compose.staging.yml down && docker-compose -f docker-compose.prod.yml down && docker-compose -f docker-compose.test.yml down", "test:e2e": "cd frontend && npm run test:e2e", "test:coverage": "npm run test:frontend:coverage && npm run test:backend:coverage", "test:frontend:coverage": "cd frontend && npm run test:coverage", "test:backend:coverage": "cd backend && python -m pytest --cov=app --cov-report=html", "test:integration": "docker-compose -f docker-compose.test.yml up -d && sleep 30 && npm run test:run:integration && docker-compose -f docker-compose.test.yml down", "test:run:integration": "cd backend && python -m pytest tests/integration/", "lint:fix": "npm run lint:frontend:fix && npm run lint:backend:fix", "lint:frontend:fix": "cd frontend && npm run lint:fix", "lint:backend:fix": "cd backend && black . && isort .", "clean:cache": "rm -rf frontend/node_modules/.vite admin-app/node_modules/.vite backend/.pytest_cache", "clean:logs": "rm -rf logs/* || true", "verify:env": "node scripts/validate-env.js", "monitor": "node scripts/build-monitor.js", "security:audit": "npm audit && cd frontend && npm audit && cd ../admin-app && npm audit && cd ../backend && safety check", "security:fix": "npm audit fix && cd frontend && npm audit fix && cd ../admin-app && npm audit fix", "performance:audit": "npm run build:analyze && npm run monitor", "update:deps": "npm update && cd frontend && npm update && cd ../admin-app && npm update", "backup": "docker exec ace-social-mongodb-prod mongodump --out /backups/$(date +%Y%m%d_%H%M%S)", "restore": "echo 'Usage: npm run restore -- <backup_directory>'"}, "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "lodash": "^4.17.21", "react-error-boundary": "^6.0.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/Tayyabjv1/Social-media-Platform"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}