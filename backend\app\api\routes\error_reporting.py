"""
Enhanced Error Reporting API
Provides detailed error tracking and analysis for the frontend application
"""

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import json
import logging
from datetime import datetime
import traceback
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/errors", tags=["error-reporting"])

class ErrorInfo(BaseModel):
    """Enhanced error information model"""
    type: str = Field(..., description="Error type (javascript, promise_rejection, etc.)")
    message: str = Field(..., description="Error message")
    filename: Optional[str] = Field(None, description="Source filename")
    lineno: Optional[int] = Field(None, description="Line number")
    colno: Optional[int] = Field(None, description="Column number")
    stack: Optional[str] = Field(None, description="Stack trace")
    componentStack: Optional[str] = Field(None, description="React component stack")
    timestamp: int = Field(..., description="Error timestamp")
    url: str = Field(..., description="Page URL where error occurred")
    userAgent: str = Field(..., description="User agent string")
    sessionId: Optional[str] = Field(None, description="Session ID")
    errorId: Optional[str] = Field(None, description="Unique error ID")
    component: Optional[str] = Field(None, description="React component name")
    module: Optional[str] = Field(None, description="Module name")
    severity: str = Field(default="medium", description="Error severity")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    browserInfo: Optional[Dict[str, Any]] = Field(None, description="Browser information")
    performanceInfo: Optional[Dict[str, Any]] = Field(None, description="Performance metrics")

class ErrorReport(BaseModel):
    """Error report model"""
    errorReference: str = Field(..., description="Detailed error reference")
    errorInfo: ErrorInfo = Field(..., description="Error information")
    sessionId: str = Field(..., description="Session ID")

class ErrorAnalysis(BaseModel):
    """Error analysis response"""
    errorReference: str
    analysis: Dict[str, Any]
    recommendations: List[str]
    severity: str
    category: str

@router.post("/report", response_model=Dict[str, str])
async def report_error(error_report: ErrorReport, request: Request):
    """
    Report a detailed error with comprehensive analysis
    """
    try:
        # Extract client IP
        client_ip = request.client.host
        
        # Enhance error information
        enhanced_error = {
            "errorReference": error_report.errorReference,
            "timestamp": datetime.now().isoformat(),
            "clientIp": client_ip,
            "sessionId": error_report.sessionId,
            "errorInfo": error_report.errorInfo.dict(),
            "analysis": _analyze_error(error_report.errorInfo),
            "environment": {
                "hostname": request.url.hostname,
                "path": request.url.path,
                "method": request.method,
                "headers": dict(request.headers)
            }
        }
        
        # Log error with detailed information
        logger.error(f"Frontend Error Reported: {error_report.errorReference}")
        logger.error(f"Error Details: {json.dumps(enhanced_error, indent=2)}")
        
        # Save error to file for analysis (in production, use proper error tracking service)
        _save_error_report(enhanced_error)
        
        # Generate recommendations
        recommendations = _generate_recommendations(error_report.errorInfo)
        
        return {
            "status": "success",
            "errorReference": error_report.errorReference,
            "message": f"Error reported successfully with reference: {error_report.errorReference}",
            "recommendations": "; ".join(recommendations)
        }
        
    except Exception as e:
        logger.error(f"Failed to process error report: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail="Failed to process error report")

@router.get("/analyze/{error_reference}", response_model=ErrorAnalysis)
async def analyze_error(error_reference: str):
    """
    Analyze a specific error by reference
    """
    try:
        # Load error from storage (simplified for demo)
        error_data = _load_error_report(error_reference)
        
        if not error_data:
            raise HTTPException(status_code=404, detail="Error reference not found")
        
        analysis = _analyze_error(ErrorInfo(**error_data["errorInfo"]))
        recommendations = _generate_recommendations(ErrorInfo(**error_data["errorInfo"]))
        
        return ErrorAnalysis(
            errorReference=error_reference,
            analysis=analysis,
            recommendations=recommendations,
            severity=error_data["errorInfo"].get("severity", "medium"),
            category=analysis.get("category", "unknown")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to analyze error {error_reference}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to analyze error")

@router.get("/summary")
async def get_error_summary():
    """
    Get error summary and statistics
    """
    try:
        # In production, this would query a proper database
        summary = {
            "totalErrors": 0,
            "errorsByType": {},
            "errorsBySeverity": {},
            "recentErrors": [],
            "topErrors": [],
            "recommendations": [
                "Implement comprehensive error boundaries",
                "Add proper error handling in async operations",
                "Use try-catch blocks around risky operations",
                "Validate data before processing",
                "Add loading states and error fallbacks"
            ]
        }
        
        return summary
        
    except Exception as e:
        logger.error(f"Failed to get error summary: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get error summary")

def _analyze_error(error_info: ErrorInfo) -> Dict[str, Any]:
    """
    Analyze error and provide detailed insights
    """
    analysis = {
        "category": "unknown",
        "likelySource": "unknown",
        "impact": "medium",
        "frequency": "unknown",
        "patterns": []
    }
    
    # Analyze error message
    message = error_info.message.lower()
    
    if "chunk" in message and "load" in message:
        analysis.update({
            "category": "chunk_loading",
            "likelySource": "Code splitting or lazy loading issue",
            "impact": "high",
            "patterns": ["Dynamic import failure", "Network connectivity issue"]
        })
    elif "network" in message or "fetch" in message:
        analysis.update({
            "category": "network",
            "likelySource": "API or network connectivity issue",
            "impact": "medium",
            "patterns": ["API endpoint unavailable", "Network timeout"]
        })
    elif "undefined" in message or "null" in message:
        analysis.update({
            "category": "null_reference",
            "likelySource": "Undefined variable or null reference",
            "impact": "medium",
            "patterns": ["Missing null checks", "Uninitialized variables"]
        })
    elif "websocket" in message:
        analysis.update({
            "category": "websocket",
            "likelySource": "WebSocket connection issue",
            "impact": "low",
            "patterns": ["Serverless platform limitation", "Connection timeout"]
        })
    elif error_info.filename and "config" in error_info.filename:
        analysis.update({
            "category": "configuration",
            "likelySource": "Configuration or environment issue",
            "impact": "high",
            "patterns": ["Environment variable missing", "Configuration error"]
        })
    
    # Analyze component stack
    if error_info.componentStack:
        if "ErrorBoundary" in error_info.componentStack:
            analysis["patterns"].append("Error boundary triggered")
        if "Suspense" in error_info.componentStack:
            analysis["patterns"].append("Suspense boundary issue")
    
    return analysis

def _generate_recommendations(error_info: ErrorInfo) -> List[str]:
    """
    Generate specific recommendations based on error analysis
    """
    recommendations = []
    message = error_info.message.lower()
    
    if "chunk" in message and "load" in message:
        recommendations.extend([
            "Check network connectivity and CDN availability",
            "Implement retry logic for chunk loading",
            "Add error boundaries around lazy-loaded components",
            "Consider preloading critical chunks"
        ])
    elif "websocket" in message:
        recommendations.extend([
            "Implement fallback mode for serverless platforms",
            "Add WebSocket connection retry logic",
            "Use polling as fallback for real-time updates",
            "Check WebSocket endpoint availability"
        ])
    elif "undefined" in message or "null" in message:
        recommendations.extend([
            "Add null/undefined checks before accessing properties",
            "Use optional chaining (?.) operator",
            "Initialize variables with default values",
            "Add TypeScript for better type safety"
        ])
    elif "network" in message or "fetch" in message:
        recommendations.extend([
            "Implement proper error handling for API calls",
            "Add retry logic with exponential backoff",
            "Show user-friendly error messages",
            "Check API endpoint availability"
        ])
    else:
        recommendations.extend([
            "Add comprehensive error logging",
            "Implement error boundaries",
            "Add proper error handling in components",
            "Use try-catch blocks around risky operations"
        ])
    
    return recommendations

def _save_error_report(error_data: Dict[str, Any]):
    """
    Save error report to file (in production, use proper storage)
    """
    try:
        # Create errors directory if it doesn't exist
        os.makedirs("logs/errors", exist_ok=True)
        
        # Save to file with timestamp
        filename = f"logs/errors/{error_data['errorReference']}.json"
        with open(filename, 'w') as f:
            json.dump(error_data, f, indent=2)
            
    except Exception as e:
        logger.error(f"Failed to save error report: {str(e)}")

def _load_error_report(error_reference: str) -> Optional[Dict[str, Any]]:
    """
    Load error report from file
    """
    try:
        filename = f"logs/errors/{error_reference}.json"
        if os.path.exists(filename):
            with open(filename, 'r') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load error report: {str(e)}")
    
    return None
